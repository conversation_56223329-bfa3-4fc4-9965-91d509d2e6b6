/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Arial', sans-serif;
}

body {
    background-color: #f0f0f0;
    display: flex;
    justify-content: center;
    padding: 20px;
}

.game-container {
    width: 800px;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    padding: 20px;
}

/* 游戏标题和计分板 */
.game-header {
    text-align: center;
    margin-bottom: 20px;
}

.game-header h1 {
    color: #333;
    margin-bottom: 15px;
}

.score-board {
    display: flex;
    justify-content: space-around;
    margin-bottom: 15px;
    font-size: 18px;
}

.player-score, .ai-score {
    padding: 10px 15px;
    border-radius: 5px;
}

.player-score {
    background-color: #e6f7ff;
    border: 1px solid #91d5ff;
}

.ai-score {
    background-color: #fff7e6;
    border: 1px solid #ffd591;
}

.label {
    font-weight: bold;
    margin-right: 5px;
}

/* 难度选择器 */
.difficulty-selector {
    margin-bottom: 15px;
}

.difficulty-selector select {
    padding: 5px 10px;
    border-radius: 5px;
    border: 1px solid #d9d9d9;
    background-color: white;
    cursor: pointer;
}

/* 游戏区域 */
.game-area-container {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
}

#game-canvas {
    border: 2px solid #333;
    background-color: #f9f9f9;
}

/* 游戏控制按钮 */
.game-controls {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 20px;
}

.game-controls button {
    padding: 8px 16px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.2s;
}

#start-btn {
    background-color: #52c41a;
    color: white;
}

#pause-btn {
    background-color: #faad14;
    color: white;
}

#restart-btn {
    background-color: #1890ff;
    color: white;
}

.game-controls button:hover {
    opacity: 0.9;
    transform: scale(1.05);
}

/* 游戏说明 */
.game-instructions {
    background-color: #f6ffed;
    border: 1px solid #b7eb8f;
    border-radius: 5px;
    padding: 15px;
}

.game-instructions h3 {
    color: #52c41a;
    margin-bottom: 10px;
}

.game-instructions p {
    margin-bottom: 8px;
    line-height: 1.5;
}

/* 游戏结束模态框 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 100;
    justify-content: center;
    align-items: center;
}

.modal-content {
    background-color: white;
    padding: 30px;
    border-radius: 10px;
    text-align: center;
    width: 400px;
}

#game-result {
    font-size: 24px;
    margin-bottom: 15px;
    color: #333;
}

#final-score {
    font-size: 18px;
    margin-bottom: 20px;
}

#play-again-btn {
    padding: 10px 20px;
    background-color: #1890ff;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.2s;
}

#play-again-btn:hover {
    opacity: 0.9;
    transform: scale(1.05);
}