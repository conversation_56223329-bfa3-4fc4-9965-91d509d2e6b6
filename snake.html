<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>超级贪吃蛇 - 人机对战</title>
    <link rel="stylesheet" href="snake.css">
</head>
<body>
    <div class="game-container">
        <div class="game-header">
            <h1>超级贪吃蛇 - 人机对战</h1>
            <div class="score-board">
                <div class="player-score">
                    <span class="label">玩家得分:</span>
                    <span id="player-score">0</span>
                    <span class="label">长度:</span>
                    <span id="player-length">1</span>
                </div>
                <div class="ai-score">
                    <span class="label">AI得分:</span>
                    <span id="ai-score">0</span>
                    <span class="label">长度:</span>
                    <span id="ai-length">1</span>
                </div>
            </div>
            <div class="difficulty-selector">
                <label for="difficulty">AI难度:</label>
                <select id="difficulty">
                    <option value="easy">简单</option>
                    <option value="medium" selected>中等</option>
                    <option value="hard">困难</option>
                </select>
            </div>
        </div>
        
        <div class="game-area-container">
            <canvas id="game-canvas" width="600" height="400"></canvas>
        </div>
        
        <div class="game-controls">
            <button id="start-btn">开始游戏</button>
            <button id="pause-btn">暂停</button>
            <button id="restart-btn">重新开始</button>
        </div>
        
        <div class="game-instructions">
            <h3>游戏说明</h3>
            <p>使用键盘方向键 ↑ ↓ ← → 控制玩家蛇移动</p>
            <p>吃到食物可以增加长度和得分</p>
            <p>撞到墙壁、自己的身体或AI蛇会导致游戏结束</p>
            <p>选择不同的AI难度来挑战自己</p>
        </div>
    </div>
    
    <div id="game-over-modal" class="modal">
        <div class="modal-content">
            <h2 id="game-result">游戏结束</h2>
            <p id="final-score"></p>
            <button id="play-again-btn">再玩一次</button>
        </div>
    </div>
    
    <script src="snake.js"></script>
</body>
</html>