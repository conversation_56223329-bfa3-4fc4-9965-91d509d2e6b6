/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

/* 容器样式 */
.container {
    max-width: 600px;
    margin: 40px auto;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
}

/* 标题样式 */
header {
    text-align: center;
    margin-bottom: 20px;
}

header h1 {
    color: #2c3e50;
    font-size: 28px;
    font-weight: 600;
}

/* 输入框和按钮样式 */
.todo-form {
    display: flex;
    margin-bottom: 20px;
}

#todo-input {
    flex: 1;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 4px 0 0 4px;
    font-size: 16px;
    outline: none;
    transition: border-color 0.3s;
}

#todo-input:focus {
    border-color: #3498db;
}

#add-button {
    background-color: #3498db;
    color: white;
    border: none;
    padding: 0 20px;
    border-radius: 0 4px 4px 0;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s;
}

#add-button:hover {
    background-color: #2980b9;
}

/* 过滤按钮样式 */
.todo-filters {
    display: flex;
    justify-content: center;
    margin-bottom: 15px;
}

.filter-btn {
    background-color: transparent;
    border: 1px solid #ddd;
    padding: 6px 12px;
    margin: 0 5px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s;
}

.filter-btn:hover {
    border-color: #3498db;
    color: #3498db;
}

.filter-btn.active {
    background-color: #3498db;
    border-color: #3498db;
    color: white;
}

/* 待办事项列表样式 */
#todo-list {
    list-style-type: none;
    margin-bottom: 20px;
}

#todo-list li {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    border-bottom: 1px solid #eee;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

#todo-list li:last-child {
    border-bottom: none;
}

.todo-checkbox {
    margin-right: 10px;
    width: 20px;
    height: 20px;
    cursor: pointer;
}

.todo-text {
    flex: 1;
    font-size: 16px;
    word-break: break-word;
    transition: color 0.3s, text-decoration 0.3s;
}

.completed .todo-text {
    color: #888;
    text-decoration: line-through;
}

.delete-btn {
    background-color: transparent;
    color: #e74c3c;
    border: none;
    cursor: pointer;
    font-size: 18px;
    opacity: 0.6;
    transition: opacity 0.3s;
    padding: 0 5px;
}

.delete-btn:hover {
    opacity: 1;
}

/* 底部样式 */
.todo-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 15px;
    border-top: 1px solid #eee;
    font-size: 14px;
    color: #777;
}

#clear-completed {
    background-color: transparent;
    border: none;
    color: #e74c3c;
    cursor: pointer;
    font-size: 14px;
    transition: opacity 0.3s;
}

#clear-completed:hover {
    opacity: 0.8;
}

/* 响应式设计 */
@media (max-width: 650px) {
    .container {
        margin: 20px 10px;
        border-radius: 5px;
    }
    
    header h1 {
        font-size: 24px;
    }
    
    #todo-input, #add-button {
        font-size: 14px;
    }
    
    .todo-text {
        font-size: 14px;
    }
}