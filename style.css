* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    color: white;
}

.game-container {
    text-align: center;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
    border: 1px solid rgba(255, 255, 255, 0.18);
}

h1 {
    margin-bottom: 20px;
    font-size: 2.5em;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.game-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.score-board {
    display: flex;
    gap: 40px;
}

.player-score, .ai-score {
    background: rgba(255, 255, 255, 0.2);
    padding: 15px 25px;
    border-radius: 10px;
    min-width: 120px;
}

.player-score h3, .ai-score h3 {
    margin-bottom: 5px;
    font-size: 1.2em;
}

.player-score span, .ai-score span {
    font-size: 1.5em;
    font-weight: bold;
}

.game-controls {
    display: flex;
    gap: 10px;
}

button {
    padding: 10px 20px;
    font-size: 16px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

button:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

button:active {
    transform: translateY(0);
}

button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

#game-canvas {
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 10px;
    background: rgba(0, 0, 0, 0.8);
    margin: 20px 0;
}

.game-status {
    margin-top: 20px;
}

#game-message {
    font-size: 1.2em;
    padding: 10px 20px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    display: inline-block;
}

@media (max-width: 768px) {
    .game-container {
        padding: 20px;
        margin: 10px;
    }
    
    .game-info {
        flex-direction: column;
        gap: 20px;
    }
    
    .score-board {
        gap: 20px;
    }
    
    #game-canvas {
        width: 100%;
        max-width: 400px;
        height: 300px;
    }
}