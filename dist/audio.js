/**
 * @class AudioManager
 * @description Manages all audio aspects of the game.
 * It uses the Web Audio API to generate sounds dynamically.
 */
class AudioManager {
    constructor() {
        this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
        this.masterVolume = 0.3; // Master volume
        
        // Sound effects definitions
        this.sounds = {
            click: this.createSound({ frequency: 440, duration: 0.05, type: 'triangle' }),
            eat: this.createSound({ frequency: 587.33, duration: 0.1, type: 'sine' }),
            gameOver: this.createSound({ frequency: 164.81, duration: 0.5, type: 'sawtooth', volume: 0.7 })
        };
    }

    /**
     * Creates a sound generator function.
     * @param {object} options - Sound options.
     * @param {number} options.frequency - The frequency of the tone in Hertz.
     * @param {number} options.duration - The duration of the sound in seconds.
     * @param {string} options.type - The waveform type (e.g., 'sine', 'square').
     * @param {number} [options.volume=1] - Relative volume multiplier.
     * @returns {function} A function that plays the sound when called.
     */
    createSound({ frequency, duration, type, volume = 1 }) {
        return () => {
            const oscillator = this.audioContext.createOscillator();
            const gainNode = this.audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(this.audioContext.destination);

            oscillator.type = type;
            oscillator.frequency.setValueAtTime(frequency, this.audioContext.currentTime);
            
            const effectiveVolume = this.masterVolume * volume;
            gainNode.gain.setValueAtTime(effectiveVolume, this.audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.001, this.audioContext.currentTime + duration);

            oscillator.start();
            oscillator.stop(this.audioContext.currentTime + duration);
        };
    }

    /**
     * Plays a predefined sound.
     * @param {string} soundName - The name of the sound to play ('click', 'eat', 'gameOver').
     */
    playSound(soundName) {
        if (this.sounds[soundName]) {
            this.sounds[soundName]();
        }
    }
    
    // Convenience methods for playing specific sounds
    playClickSound() { this.playSound('click'); }
    playEatSound() { this.playSound('eat'); }
    playGameOverSound() { this.playSound('gameOver'); }
}

/**
 * @class VisualEffects
 * @description Manages visual feedback like particle explosions and screen shake.
 * It's designed to be self-contained, handling its own animation updates and drawing.
 */
class VisualEffects {
    constructor() {
        this.particles = [];
        this.shake = {
            intensity: 0,
            duration: 0,
            time: 0
        };
    }

    /**
     * Creates a particle explosion effect. Called when the snake eats food.
     * @param {number} x - The x-coordinate of the explosion center.
     * @param {number} y - The y-coordinate of the explosion center.
     * @param {string} [color='#ffff00'] - The color of the particles.
     * @param {number} [count=15] - The number of particles to create.
     */
    createExplosion(x, y, color = '#ffff00', count = 15) {
        for (let i = 0; i < count; i++) {
            this.particles.push({
                x,
                y,
                vx: (Math.random() - 0.5) * (Math.random() * 6),
                vy: (Math.random() - 0.5) * (Math.random() * 6),
                life: 1, // Lifetime of the particle
                size: Math.random() * 3 + 1,
                color,
            });
        }
    }

    /**
     * Initiates a screen shake effect. Called on game over.
     * @param {number} intensity - The maximum pixel displacement of the shake.
     * @param {number} duration - The duration of the shake in seconds.
     */
    shakeScreen(intensity, duration) {
        this.shake.intensity = intensity;
        this.shake.duration = duration;
        this.shake.time = duration; // Reset the shake timer
    }

    /**
     * Updates the state of all visual effects (particles and shake).
     * This method should be called once per frame from the main game loop.
     * @param {number} dt - The delta time in seconds since the last frame.
     */
    update(dt) {
        // Update particles' position and lifetime
        for (let i = this.particles.length - 1; i >= 0; i--) {
            const p = this.particles[i];
            p.x += p.vx;
            p.y += p.vy;
            p.life -= dt * 2; // Particles fade over ~0.5 seconds
            if (p.life <= 0) {
                this.particles.splice(i, 1); // Remove dead particles
            }
        }

        // Update screen shake timer
        if (this.shake.time > 0) {
            this.shake.time -= dt;
        }
    }

    /**
     * Applies the screen shake transformation to the canvas context.
     * This should be called before drawing the main game scene.
     * @param {CanvasRenderingContext2D} ctx - The canvas rendering context.
     */
    applyShake(ctx) {
        if (this.shake.time > 0) {
            // As shake time decreases, its intensity fades
            const currentIntensity = this.shake.intensity * (this.shake.time / this.shake.duration);
            const shakeX = (Math.random() - 0.5) * currentIntensity;
            const shakeY = (Math.random() - 0.5) * currentIntensity;
            ctx.translate(shakeX, shakeY);
        }
    }

    /**
     * Draws all visual effects (e.g., particles) to the canvas.
     * This should be called after drawing the main game scene.
     * @param {CanvasRenderingContext2D} ctx - The canvas rendering context.
     */
    draw(ctx) {
        // Draw all active particles
        for (const p of this.particles) {
            ctx.fillStyle = p.color;
            ctx.globalAlpha = p.life; // Fade out effect
            ctx.beginPath();
            ctx.arc(p.x, p.y, p.size, 0, Math.PI * 2);
            ctx.fill();
        }
        ctx.globalAlpha = 1; // Reset alpha for other drawing operations
    }
}

// 导出供其他模块使用
window.AudioManager = AudioManager;
window.VisualEffects = VisualEffects;