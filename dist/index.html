<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>超级贪吃蛇 - 人机对战</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="game-container">
        <header>
            <h1>超级贪吃蛇 - 人机对战</h1>
            <div class="game-info">
                <div class="score-board">
                    <div class="player-score">
                        <h3>玩家</h3>
                        <span id="player-score">0</span>
                    </div>
                    <div class="ai-score">
                        <h3>AI</h3>
                        <span id="ai-score">0</span>
                    </div>
                </div>
                <div class="game-controls">
                    <button id="startBtn">开始游戏</button>
                    <button id="pauseBtn" disabled>暂停</button>
                    <button id="resetBtn">重置</button>
                </div>
            </div>
        </header>

        <main>
            <canvas id="game-canvas" width="600" height="600"></canvas>
            <div class="game-status">
                <div id="game-message">按开始游戏按钮开始对战</div>
            </div>
            <div class="controls">
                <div class="control-group">
                    <h3>游戏设置</h3>
                    <label>
                        游戏速度:
                        <select id="speedSelect">
                            <option value="200">慢速</option>
                            <option value="150" selected>中速</option>
                            <option value="100">快速</option>
                            <option value="50">极速</option>
                        </select>
                    </label>
                </div>

                <div class="control-group">
                    <h3>操作说明</h3>
                    <ul>
                        <li>方向键：控制玩家蛇移动</li>
                        <li>空格键：暂停/继续游戏</li>
                        <li>红色蛇：AI对手</li>
                        <li>绿色蛇：玩家控制</li>
                    </ul>
                </div>
            </div>
        </main>
    </div>

    <script src="audio.js"></script>
    <script src="game.js"></script>
</body>
</html>