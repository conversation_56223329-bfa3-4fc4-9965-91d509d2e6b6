<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>待办事项清单</title>
    <link rel="stylesheet" href="todo.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>我的待办事项</h1>
        </header>
        
        <div class="todo-form">
            <input type="text" id="todo-input" placeholder="添加新的待办事项..." autofocus>
            <button id="add-button">添加</button>
        </div>
        
        <div class="todo-filters">
            <button class="filter-btn active" data-filter="all">全部</button>
            <button class="filter-btn" data-filter="active">未完成</button>
            <button class="filter-btn" data-filter="completed">已完成</button>
        </div>
        
        <ul id="todo-list">
            <!-- 待办事项将通过JavaScript动态添加 -->
        </ul>
        
        <div class="todo-footer">
            <span id="items-left">0 项待办</span>
            <button id="clear-completed">清除已完成</button>
        </div>
    </div>
    
    <script src="todo.js"></script>
</body>
</html>