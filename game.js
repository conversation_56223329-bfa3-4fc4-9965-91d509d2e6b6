class SnakeGame {
    constructor() {
        this.canvas = document.getElementById('game-canvas');
        this.ctx = this.canvas.getContext('2d');
        this.startBtn = document.getElementById('startBtn');
        this.pauseBtn = document.getElementById('pauseBtn');
        this.resetBtn = document.getElementById('resetBtn');
        this.playerScoreEl = document.getElementById('player-score');
        this.aiScoreEl = document.getElementById('ai-score');
        this.gameMessageEl = document.getElementById('game-message');

        this.gridSize = 20;
        this.tileCount = {
            x: this.canvas.width / this.gridSize,
            y: this.canvas.height / this.gridSize
        };

        this.gameState = 'stopped'; // stopped, playing, paused, gameOver
        this.gameLoopId = null;
        this.lastTime = 0; // 用于计算dt
        this.lastUpdateTime = 0; // 用于控制游戏速度
        this.gameSpeed = 150; // 默认速度

        // 音效和视觉效果管理器
        this.audioManager = new AudioManager();
        this.visualEffects = new VisualEffects();

        this.initializeGame();
        this.bindEvents();
    }

    initializeGame() {
        // 玩家蛇
        this.playerSnake = {
            body: [{ x: 10, y: 10 }],
            direction: { x: 1, y: 0 },
            color: '#00ff00',
            score: 0
        };

        // AI蛇
        this.aiSnake = {
            body: [{ x: 30, y: 20 }],
            direction: { x: -1, y: 0 },
            color: '#ff0000',
            score: 0
        };

        this.food = this.generateFood();
        this.updateScoreDisplay();
    }

    generateFood() {
        let food;
        do {
            food = {
                x: Math.floor(Math.random() * this.tileCount.x),
                y: Math.floor(Math.random() * this.tileCount.y)
            };
        } while (this.isPositionOccupied(food));
        return food;
    }

    isPositionOccupied(pos) {
        // 检查玩家蛇
        for (let segment of this.playerSnake.body) {
            if (segment.x === pos.x && segment.y === pos.y) return true;
        }
        // 检查AI蛇
        for (let segment of this.aiSnake.body) {
            if (segment.x === pos.x && segment.y === pos.y) return true;
        }
        return false;
    }

    bindEvents() {
        this.startBtn.addEventListener('click', () => {
            this.audioManager.playClickSound();
            this.startGame();
        });
        this.pauseBtn.addEventListener('click', () => {
            this.audioManager.playClickSound();
            this.pauseGame();
        });
        this.resetBtn.addEventListener('click', () => {
            this.audioManager.playClickSound();
            this.resetGame();
        });

        // 速度选择器
        const speedSelect = document.getElementById('speedSelect');
        if (speedSelect) {
            speedSelect.addEventListener('change', (e) => {
                this.gameSpeed = parseInt(e.target.value);
            });
        }

        // 键盘控制
        document.addEventListener('keydown', (e) => this.handleKeyPress(e));
    }

    handleKeyPress(e) {
        // 空格键暂停/继续
        if (e.key === ' ') {
            e.preventDefault();
            if (this.gameState === 'playing') {
                this.pauseGame();
            } else if (this.gameState === 'paused') {
                this.startGame();
            }
            return;
        }

        if (this.gameState !== 'playing') return;

        const newDirection = { ...this.playerSnake.direction };
        
        switch(e.key) {
            case 'ArrowUp':
                if (this.playerSnake.direction.y === 0) {
                    newDirection.x = 0;
                    newDirection.y = -1;
                }
                break;
            case 'ArrowDown':
                if (this.playerSnake.direction.y === 0) {
                    newDirection.x = 0;
                    newDirection.y = 1;
                }
                break;
            case 'ArrowLeft':
                if (this.playerSnake.direction.x === 0) {
                    newDirection.x = -1;
                    newDirection.y = 0;
                }
                break;
            case 'ArrowRight':
                if (this.playerSnake.direction.x === 0) {
                    newDirection.x = 1;
                    newDirection.y = 0;
                }
                break;
        }

        this.playerSnake.direction = newDirection;
    }

    startGame() {
        if (this.gameState === 'playing') return;
        
        this.gameState = 'playing';
        this.gameMessageEl.textContent = '游戏进行中！使用方向键控制';
        this.startBtn.disabled = true;

        this.lastTime = performance.now();
        this.gameLoopId = requestAnimationFrame((time) => this.gameLoop(time));
    }

    pauseGame() {
        if (this.gameState === 'playing') {
            this.gameState = 'paused';
            cancelAnimationFrame(this.gameLoopId);
            this.gameMessageEl.textContent = '游戏暂停';
            this.startBtn.disabled = false;
            this.startBtn.textContent = '继续游戏';
        }
    }

    resetGame() {
        this.gameState = 'stopped';
        if (this.gameLoopId) {
            cancelAnimationFrame(this.gameLoopId);
        }
        this.initializeGame();
        this.gameMessageEl.textContent = '按开始游戏按钮开始';
        this.startBtn.disabled = false;
        this.startBtn.textContent = '开始游戏';
        this.draw();
    }

    gameLoop(currentTime) {
        if (this.gameState !== 'playing') return;

        const dt = (currentTime - this.lastTime) / 1000; // Delta time in seconds
        this.lastTime = currentTime;

        // 根据游戏速度控制更新频率
        if (currentTime - this.lastUpdateTime < this.gameSpeed) {
            this.gameLoopId = requestAnimationFrame((time) => this.gameLoop(time));
            return;
        }
        this.lastUpdateTime = currentTime;

        this.update(dt);
        this.draw();

        this.gameLoopId = requestAnimationFrame((time) => this.gameLoop(time));
    }

    update(dt) {
        this.visualEffects.update(dt); // 更新视觉效果

        this.moveSnake(this.playerSnake);
        this.moveSnake(this.aiSnake);
        
        this.aiMove();
        
        if (this.checkCollision(this.playerSnake)) {
            this.endGame('AI获胜！');
            return;
        }
        
        if (this.checkCollision(this.aiSnake)) {
            this.endGame('玩家获胜！');
            return;
        }

        this.checkFood();
    }

    moveSnake(snake) {
        const head = { ...snake.body[0] };
        head.x += snake.direction.x;
        head.y += snake.direction.y;

        // 边界检查
        if (head.x < 0) head.x = this.tileCount.x - 1;
        if (head.x >= this.tileCount.x) head.x = 0;
        if (head.y < 0) head.y = this.tileCount.y - 1;
        if (head.y >= this.tileCount.y) head.y = 0;

        snake.body.unshift(head);
        
        // 如果没有吃到食物，移除尾部
        if (!(head.x === this.food.x && head.y === this.food.y)) {
            snake.body.pop();
        }
    }

    checkCollision(snake) {
        const head = snake.body[0];
        
        // 检查是否撞到自己
        for (let i = 1; i < snake.body.length; i++) {
            if (head.x === snake.body[i].x && head.y === snake.body[i].y) {
                return true;
            }
        }

        // 检查是否撞到另一条蛇
        const otherSnake = snake === this.playerSnake ? this.aiSnake : this.playerSnake;
        for (let segment of otherSnake.body) {
            if (head.x === segment.x && head.y === segment.y) {
                return true;
            }
        }

        return false;
    }

    checkFood() {
        const playerHead = this.playerSnake.body[0];
        const aiHead = this.aiSnake.body[0];
        const foodPos = {
            x: this.food.x * this.gridSize + this.gridSize / 2,
            y: this.food.y * this.gridSize + this.gridSize / 2
        };

        if (playerHead.x === this.food.x && playerHead.y === this.food.y) {
            this.playerSnake.score += 10;
            this.audioManager.playEatSound();
            this.visualEffects.createExplosion(foodPos.x, foodPos.y);
            this.food = this.generateFood();
        }

        if (aiHead.x === this.food.x && aiHead.y === this.food.y) {
            this.aiSnake.score += 10;
            this.audioManager.playEatSound();
            this.visualEffects.createExplosion(foodPos.x, foodPos.y, this.aiSnake.color);
            this.food = this.generateFood();
        }

        this.updateScoreDisplay();
    }

    aiMove() {
        const head = this.aiSnake.body[0];
        const target = this.food;
        
        // 简单的AI策略：向食物移动，同时避免碰撞
        let bestDirection = null;
        let minDistance = Infinity;

        const directions = [
            { x: 0, y: -1 }, // 上
            { x: 1, y: 0 },  // 右
            { x: 0, y: 1 },  // 下
            { x: -1, y: 0 }  // 左
        ];

        for (let dir of directions) {
            // 避免反向移动
            if (dir.x === -this.aiSnake.direction.x && dir.y === -this.aiSnake.direction.y) {
                continue;
            }

            const newHead = {
                x: head.x + dir.x,
                y: head.y + dir.y
            };

            // 边界处理
            if (newHead.x < 0) newHead.x = this.tileCount.x - 1;
            if (newHead.x >= this.tileCount.x) newHead.x = 0;
            if (newHead.y < 0) newHead.y = this.tileCount.y - 1;
            if (newHead.y >= this.tileCount.y) newHead.y = 0;

            // 检查是否会碰撞
            if (this.willCollide(newHead, this.aiSnake)) {
                continue;
            }

            // 计算到食物的距离
            const distance = Math.abs(newHead.x - target.x) + Math.abs(newHead.y - target.y);
            
            if (distance < minDistance) {
                minDistance = distance;
                bestDirection = dir;
            }
        }

        // 如果没有好的方向，保持当前方向
        if (bestDirection) {
            this.aiSnake.direction = bestDirection;
        }
    }

    willCollide(newHead, snake) {
        // 检查是否会撞到自己
        for (let segment of snake.body) {
            if (newHead.x === segment.x && newHead.y === segment.y) {
                return true;
            }
        }

        // 检查是否会撞到另一条蛇
        const otherSnake = snake === this.playerSnake ? this.aiSnake : this.playerSnake;
        for (let segment of otherSnake.body) {
            if (newHead.x === segment.x && newHead.y === segment.y) {
                return true;
            }
        }

        return false;
    }

    endGame(message) {
        if (this.gameState === 'gameOver') return; // 防止重复触发
        this.gameState = 'gameOver';
        if (this.gameLoopId) {
            cancelAnimationFrame(this.gameLoopId);
        }
        this.gameMessageEl.textContent = message;
        this.startBtn.disabled = false;
        this.startBtn.textContent = '重新开始';
        this.audioManager.playGameOverSound();
        this.visualEffects.shakeScreen(15, 0.5);
    }

    updateScoreDisplay() {
        this.playerScoreEl.textContent = this.playerSnake.score;
        this.aiScoreEl.textContent = this.aiSnake.score;
    }

    draw() {
        this.ctx.save();
        // 应用屏幕震动
        this.visualEffects.applyShake(this.ctx);

        // 清空画布
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // 绘制网格
        this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
        this.ctx.lineWidth = 1;
        for (let i = 0; i <= this.tileCount.x; i++) {
            this.ctx.beginPath();
            this.ctx.moveTo(i * this.gridSize, 0);
            this.ctx.lineTo(i * this.gridSize, this.canvas.height);
            this.ctx.stroke();
        }
        for (let i = 0; i <= this.tileCount.y; i++) {
            this.ctx.beginPath();
            this.ctx.moveTo(0, i * this.gridSize);
            this.ctx.lineTo(this.canvas.width, i * this.gridSize);
            this.ctx.stroke();
        }

        // 绘制食物
        this.ctx.fillStyle = '#ffff00';
        this.ctx.fillRect(
            this.food.x * this.gridSize + 2,
            this.food.y * this.gridSize + 2,
            this.gridSize - 4,
            this.gridSize - 4
        );

        // 绘制玩家蛇
        this.drawSnake(this.playerSnake);
        
        // 绘制AI蛇
        this.drawSnake(this.aiSnake);
        
        // 绘制视觉效果
        this.visualEffects.draw(this.ctx);

        this.ctx.restore();
    }

    drawSnake(snake) {
        this.ctx.fillStyle = snake.color;
        
        // 绘制蛇头
        this.ctx.fillRect(
            snake.body[0].x * this.gridSize,
            snake.body[0].y * this.gridSize,
            this.gridSize,
            this.gridSize
        );
        
        // 绘制蛇身
        this.ctx.fillStyle = snake.color + '80'; // 半透明
        for (let i = 1; i < snake.body.length; i++) {
            this.ctx.fillRect(
                snake.body[i].x * this.gridSize + 2,
                snake.body[i].y * this.gridSize + 2,
                this.gridSize - 4,
                this.gridSize - 4
            );
        }
    }
}

// 初始化游戏
document.addEventListener('DOMContentLoaded', () => {
    const game = new SnakeGame();
    game.draw();
});