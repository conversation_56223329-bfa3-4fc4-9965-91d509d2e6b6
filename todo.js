/**
 * 待办事项清单应用
 */
class TodoApp {
    /**
     * 初始化应用
     */
    constructor() {
        // DOM元素
        this.todoInput = document.getElementById('todo-input');
        this.addButton = document.getElementById('add-button');
        this.todoList = document.getElementById('todo-list');
        this.filterButtons = document.querySelectorAll('.filter-btn');
        this.clearCompletedButton = document.getElementById('clear-completed');
        this.itemsLeftSpan = document.getElementById('items-left');
        
        // 应用状态
        this.todos = [];
        this.currentFilter = 'all';
        
        // 绑定事件处理器
        this.addButton.addEventListener('click', () => this.addTodo());
        this.todoInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.addTodo();
        });
        this.clearCompletedButton.addEventListener('click', () => this.clearCompleted());
        
        // 绑定过滤按钮事件
        this.filterButtons.forEach(button => {
            button.addEventListener('click', () => {
                this.setFilter(button.dataset.filter);
                this.updateFilterButtons();
                this.renderTodos();
            });
        });
        
        // 从本地存储加载数据
        this.loadFromLocalStorage();
        
        // 初始渲染
        this.renderTodos();
        this.updateItemsLeft();
    }
    
    /**
     * 添加新的待办事项
     */
    addTodo() {
        const todoText = this.todoInput.value.trim();
        
        if (todoText) {
            const newTodo = {
                id: Date.now(),
                text: todoText,
                completed: false
            };
            
            this.todos.push(newTodo);
            this.todoInput.value = '';
            this.saveToLocalStorage();
            this.renderTodos();
            this.updateItemsLeft();
        }
    }
    
    /**
     * 切换待办事项的完成状态
     * @param {number} id - 待办事项ID
     */
    toggleTodo(id) {
        this.todos = this.todos.map(todo => {
            if (todo.id === id) {
                return { ...todo, completed: !todo.completed };
            }
            return todo;
        });
        
        this.saveToLocalStorage();
        this.renderTodos();
        this.updateItemsLeft();
    }
    
    /**
     * 删除待办事项
     * @param {number} id - 待办事项ID
     */
    deleteTodo(id) {
        this.todos = this.todos.filter(todo => todo.id !== id);
        this.saveToLocalStorage();
        this.renderTodos();
        this.updateItemsLeft();
    }
    
    /**
     * 清除所有已完成的待办事项
     */
    clearCompleted() {
        this.todos = this.todos.filter(todo => !todo.completed);
        this.saveToLocalStorage();
        this.renderTodos();
        this.updateItemsLeft();
    }
    
    /**
     * 设置当前过滤器
     * @param {string} filter - 过滤器类型：'all', 'active', 'completed'
     */
    setFilter(filter) {
        this.currentFilter = filter;
    }
    
    /**
     * 更新过滤按钮的激活状态
     */
    updateFilterButtons() {
        this.filterButtons.forEach(button => {
            if (button.dataset.filter === this.currentFilter) {
                button.classList.add('active');
            } else {
                button.classList.remove('active');
            }
        });
    }
    
    /**
     * 根据当前过滤器获取待办事项
     * @returns {Array} 过滤后的待办事项数组
     */
    getFilteredTodos() {
        switch (this.currentFilter) {
            case 'active':
                return this.todos.filter(todo => !todo.completed);
            case 'completed':
                return this.todos.filter(todo => todo.completed);
            default:
                return this.todos;
        }
    }
    
    /**
     * 渲染待办事项列表
     */
    renderTodos() {
        // 清空列表
        this.todoList.innerHTML = '';
        
        // 获取过滤后的待办事项
        const filteredTodos = this.getFilteredTodos();
        
        // 如果没有待办事项，显示提示信息
        if (filteredTodos.length === 0) {
            const emptyMessage = document.createElement('li');
            emptyMessage.textContent = '没有待办事项';
            emptyMessage.style.textAlign = 'center';
            emptyMessage.style.color = '#888';
            emptyMessage.style.padding = '20px 0';
            this.todoList.appendChild(emptyMessage);
            return;
        }
        
        // 渲染每个待办事项
        filteredTodos.forEach(todo => {
            const li = document.createElement('li');
            if (todo.completed) {
                li.classList.add('completed');
            }
            
            // 创建复选框
            const checkbox = document.createElement('input');
            checkbox.type = 'checkbox';
            checkbox.className = 'todo-checkbox';
            checkbox.checked = todo.completed;
            checkbox.addEventListener('change', () => this.toggleTodo(todo.id));
            
            // 创建待办事项文本
            const span = document.createElement('span');
            span.className = 'todo-text';
            span.textContent = todo.text;
            
            // 创建删除按钮
            const deleteButton = document.createElement('button');
            deleteButton.className = 'delete-btn';
            deleteButton.innerHTML = '&times;';
            deleteButton.addEventListener('click', () => this.deleteTodo(todo.id));
            
            // 将元素添加到列表项
            li.appendChild(checkbox);
            li.appendChild(span);
            li.appendChild(deleteButton);
            
            // 将列表项添加到列表
            this.todoList.appendChild(li);
        });
    }
    
    /**
     * 更新剩余待办事项数量
     */
    updateItemsLeft() {
        const activeTodos = this.todos.filter(todo => !todo.completed);
        this.itemsLeftSpan.textContent = `${activeTodos.length} 项待办`;
    }
    
    /**
     * 保存数据到本地存储
     */
    saveToLocalStorage() {
        localStorage.setItem('todos', JSON.stringify(this.todos));
    }
    
    /**
     * 从本地存储加载数据
     */
    loadFromLocalStorage() {
        const storedTodos = localStorage.getItem('todos');
        if (storedTodos) {
            try {
                this.todos = JSON.parse(storedTodos);
            } catch (e) {
                console.error('无法解析本地存储中的待办事项数据', e);
                this.todos = [];
            }
        }
    }
}

// 当DOM加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new TodoApp();
});