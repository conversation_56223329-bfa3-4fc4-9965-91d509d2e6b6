// 游戏常量
const GRID_SIZE = 20; // 网格大小
const GRID_WIDTH = 30; // 游戏区域宽度（格子数）
const GRID_HEIGHT = 20; // 游戏区域高度（格子数）
const GAME_SPEED = 100; // 游戏基础速度（毫秒）
const DIRECTIONS = {
    UP: { x: 0, y: -1 },
    DOWN: { x: 0, y: 1 },
    LEFT: { x: -1, y: 0 },
    RIGHT: { x: 1, y: 0 }
};

// 颜色配置
const COLORS = {
    BACKGROUND: '#f9f9f9',
    GRID: '#e0e0e0',
    PLAYER_HEAD: '#0050b3',
    PLAYER_BODY: '#1890ff',
    AI_HEAD: '#ad2102',
    AI_BODY: '#fa541c',
    FOOD: '#52c41a',
    SPECIAL_FOOD: '#722ed1'
};

// 游戏状态
let gameState = {
    running: false,
    paused: false,
    gameOver: false,
    playerScore: 0,
    aiScore: 0,
    foodPosition: null,
    specialFoodPosition: null,
    specialFoodTimer: null,
    difficulty: 'medium',
    winner: null
};

// 玩家蛇
let playerSnake = {
    body: [{ x: 5, y: 10 }],
    direction: DIRECTIONS.RIGHT,
    nextDirection: DIRECTIONS.RIGHT,
    color: COLORS.PLAYER_BODY,
    headColor: COLORS.PLAYER_HEAD
};

// AI蛇
let aiSnake = {
    body: [{ x: 25, y: 10 }],
    direction: DIRECTIONS.LEFT,
    nextDirection: DIRECTIONS.LEFT,
    color: COLORS.AI_BODY,
    headColor: COLORS.AI_HEAD,
    thinkCounter: 0,
    thinkInterval: 1 // AI思考间隔，会根据难度调整
};

// 游戏元素
const canvas = document.getElementById('game-canvas');
const ctx = canvas.getContext('2d');
const startBtn = document.getElementById('start-btn');
const pauseBtn = document.getElementById('pause-btn');
const restartBtn = document.getElementById('restart-btn');
const difficultySelect = document.getElementById('difficulty');
const gameOverModal = document.getElementById('game-over-modal');
const gameResultText = document.getElementById('game-result');
const finalScoreText = document.getElementById('final-score');
const playAgainBtn = document.getElementById('play-again-btn');

// 计分板元素
const playerScoreElement = document.getElementById('player-score');
const playerLengthElement = document.getElementById('player-length');
const aiScoreElement = document.getElementById('ai-score');
const aiLengthElement = document.getElementById('ai-length');

// 游戏循环变量
let gameLoop;

// 初始化游戏
function initGame() {
    // 重置游戏状态
    gameState = {
        running: false,
        paused: false,
        gameOver: false,
        playerScore: 0,
        aiScore: 0,
        foodPosition: null,
        specialFoodPosition: null,
        specialFoodTimer: null,
        difficulty: difficultySelect.value,
        winner: null
    };

    // 重置玩家蛇
    playerSnake = {
        body: [{ x: 5, y: 10 }],
        direction: DIRECTIONS.RIGHT,
        nextDirection: DIRECTIONS.RIGHT,
        color: COLORS.PLAYER_BODY,
        headColor: COLORS.PLAYER_HEAD
    };

    // 重置AI蛇
    aiSnake = {
        body: [{ x: 25, y: 10 }],
        direction: DIRECTIONS.LEFT,
        nextDirection: DIRECTIONS.LEFT,
        color: COLORS.AI_BODY,
        headColor: COLORS.AI_HEAD,
        thinkCounter: 0,
        thinkInterval: getDifficultySettings().aiThinkInterval
    };

    // 更新计分板
    updateScoreboard();

    // 生成第一个食物
    generateFood();

    // 绘制初始游戏状态
    drawGame();
}

// 获取难度设置
function getDifficultySettings() {
    const settings = {
        easy: {
            aiThinkInterval: 3,
            aiErrorChance: 0.3,
            aiVisionRange: 5,
            gameSpeed: GAME_SPEED * 1.2
        },
        medium: {
            aiThinkInterval: 2,
            aiErrorChance: 0.15,
            aiVisionRange: 8,
            gameSpeed: GAME_SPEED
        },
        hard: {
            aiThinkInterval: 1,
            aiErrorChance: 0.05,
            aiVisionRange: 12,
            gameSpeed: GAME_SPEED * 0.8
        }
    };

    return settings[gameState.difficulty];
}

// 开始游戏
function startGame() {
    if (!gameState.running) {
        gameState.running = true;
        gameState.paused = false;
        
        // 设置游戏循环
        const difficultySettings = getDifficultySettings();
        gameLoop = setInterval(updateGame, difficultySettings.gameSpeed);
        
        // 更新按钮状态
        startBtn.disabled = true;
        pauseBtn.disabled = false;
        difficultySelect.disabled = true;
    }
}

// 暂停游戏
function pauseGame() {
    if (gameState.running && !gameState.paused) {
        gameState.paused = true;
        clearInterval(gameLoop);
        pauseBtn.textContent = '继续';
    } else if (gameState.running && gameState.paused) {
        gameState.paused = false;
        const difficultySettings = getDifficultySettings();
        gameLoop = setInterval(updateGame, difficultySettings.gameSpeed);
        pauseBtn.textContent = '暂停';
    }
}

// 重新开始游戏
function restartGame() {
    clearInterval(gameLoop);
    initGame();
    startBtn.disabled = false;
    pauseBtn.disabled = true;
    pauseBtn.textContent = '暂停';
    difficultySelect.disabled = false;
}

// 游戏结束
function endGame(winner) {
    gameState.running = false;
    gameState.gameOver = true;
    gameState.winner = winner;
    clearInterval(gameLoop);
    
    // 显示游戏结束模态框
    if (winner === 'player') {
        gameResultText.textContent = '恭喜，你赢了！';
    } else if (winner === 'ai') {
        gameResultText.textContent = 'AI赢了，再接再厉！';
    } else {
        gameResultText.textContent = '游戏结束';
    }
    
    finalScoreText.textContent = `玩家得分: ${gameState.playerScore} | AI得分: ${gameState.aiScore}`;
    gameOverModal.style.display = 'flex';
    
    // 重置按钮状态
    startBtn.disabled = false;
    pauseBtn.disabled = true;
    difficultySelect.disabled = false;
}

// 更新计分板
function updateScoreboard() {
    playerScoreElement.textContent = gameState.playerScore;
    playerLengthElement.textContent = playerSnake.body.length;
    aiScoreElement.textContent = gameState.aiScore;
    aiLengthElement.textContent = aiSnake.body.length;
}

// 生成食物
function generateFood() {
    let newFoodPosition;
    
    // 确保食物不会生成在蛇身上
    do {
        newFoodPosition = {
            x: Math.floor(Math.random() * GRID_WIDTH),
            y: Math.floor(Math.random() * GRID_HEIGHT)
        };
    } while (
        isPositionOccupied(newFoodPosition, playerSnake.body) || 
        isPositionOccupied(newFoodPosition, aiSnake.body) ||
        (gameState.specialFoodPosition && 
         newFoodPosition.x === gameState.specialFoodPosition.x && 
         newFoodPosition.y === gameState.specialFoodPosition.y)
    );
    
    gameState.foodPosition = newFoodPosition;
    
    // 有10%的几率生成特殊食物
    if (Math.random() < 0.1 && !gameState.specialFoodPosition) {
        generateSpecialFood();
    }
}

// 生成特殊食物
function generateSpecialFood() {
    let newSpecialFoodPosition;
    
    // 确保特殊食物不会生成在蛇身上或普通食物上
    do {
        newSpecialFoodPosition = {
            x: Math.floor(Math.random() * GRID_WIDTH),
            y: Math.floor(Math.random() * GRID_HEIGHT)
        };
    } while (
        isPositionOccupied(newSpecialFoodPosition, playerSnake.body) || 
        isPositionOccupied(newSpecialFoodPosition, aiSnake.body) ||
        (gameState.foodPosition && 
         newSpecialFoodPosition.x === gameState.foodPosition.x && 
         newSpecialFoodPosition.y === gameState.foodPosition.y)
    );
    
    gameState.specialFoodPosition = newSpecialFoodPosition;
    
    // 特殊食物10秒后消失
    gameState.specialFoodTimer = setTimeout(() => {
        gameState.specialFoodPosition = null;
    }, 10000);
}

// 检查位置是否被占用
function isPositionOccupied(position, bodyArray) {
    return bodyArray.some(segment => segment.x === position.x && segment.y === position.y);
}

// 检查是否吃到食物
function checkFoodCollision(head, isPlayerSnake) {
    // 检查普通食物
    if (gameState.foodPosition && head.x === gameState.foodPosition.x && head.y === gameState.foodPosition.y) {
        if (isPlayerSnake) {
            gameState.playerScore += 10;
        } else {
            gameState.aiScore += 10;
        }
        generateFood();
        return true;
    }
    
    // 检查特殊食物
    if (gameState.specialFoodPosition && head.x === gameState.specialFoodPosition.x && head.y === gameState.specialFoodPosition.y) {
        if (isPlayerSnake) {
            gameState.playerScore += 30;
        } else {
            gameState.aiScore += 30;
        }
        clearTimeout(gameState.specialFoodTimer);
        gameState.specialFoodPosition = null;
        return true;
    }
    
    return false;
}

// 检查碰撞
function checkCollision(head, isPlayerSnake) {
    const snake = isPlayerSnake ? playerSnake : aiSnake;
    const otherSnake = isPlayerSnake ? aiSnake : playerSnake;
    
    // 检查墙壁碰撞
    if (head.x < 0 || head.x >= GRID_WIDTH || head.y < 0 || head.y >= GRID_HEIGHT) {
        endGame(isPlayerSnake ? 'ai' : 'player');
        return true;
    }
    
    // 检查自身碰撞（从第二个身体段开始检查）
    for (let i = 1; i < snake.body.length; i++) {
        if (head.x === snake.body[i].x && head.y === snake.body[i].y) {
            endGame(isPlayerSnake ? 'ai' : 'player');
            return true;
        }
    }
    
    // 检查与对方蛇的碰撞
    for (let i = 0; i < otherSnake.body.length; i++) {
        if (head.x === otherSnake.body[i].x && head.y === otherSnake.body[i].y) {
            endGame(isPlayerSnake ? 'ai' : 'player');
            return true;
        }
    }
    
    return false;
}

// 更新蛇的位置
function updateSnakePosition(snake, isPlayerSnake) {
    // 更新方向
    snake.direction = snake.nextDirection;
    
    // 计算新的头部位置
    const newHead = {
        x: snake.body[0].x + snake.direction.x,
        y: snake.body[0].y + snake.direction.y
    };
    
    // 检查碰撞
    if (checkCollision(newHead, isPlayerSnake)) {
        return;
    }
    
    // 将新头部添加到身体前面
    snake.body.unshift(newHead);
    
    // 检查是否吃到食物
    if (!checkFoodCollision(newHead, isPlayerSnake)) {
        // 如果没有吃到食物，移除尾部
        snake.body.pop();
    }
}

// AI决策逻辑
function updateAIDirection() {
    // 根据难度设置AI思考频率
    aiSnake.thinkCounter++;
    if (aiSnake.thinkCounter < aiSnake.thinkInterval) {
        return;
    }
    aiSnake.thinkCounter = 0;
    
    const difficultySettings = getDifficultySettings();
    const head = aiSnake.body[0];
    let bestDirection = null;
    let bestScore = -Infinity;
    
    // 随机错误概率
    if (Math.random() < difficultySettings.aiErrorChance) {
        // AI犯错，随机选择一个方向
        const directions = Object.values(DIRECTIONS);
        const randomDirection = directions[Math.floor(Math.random() * directions.length)];
        
        // 确保不会直接撞墙或自己的身体
        const newHead = {
            x: head.x + randomDirection.x,
            y: head.y + randomDirection.y
        };
        
        if (newHead.x >= 0 && newHead.x < GRID_WIDTH && 
            newHead.y >= 0 && newHead.y < GRID_HEIGHT && 
            !isPositionOccupied(newHead, aiSnake.body.slice(1))) {
            aiSnake.nextDirection = randomDirection;
        }
        return;
    }
    
    // 评估每个可能的方向
    for (const direction of Object.values(DIRECTIONS)) {
        // 不允许180度转弯
        if (direction.x === -aiSnake.direction.x && direction.y === -aiSnake.direction.y) {
            continue;
        }
        
        const newHead = {
            x: head.x + direction.x,
            y: head.y + direction.y
        };
        
        // 检查是否会撞墙或撞到自己
        if (newHead.x < 0 || newHead.x >= GRID_WIDTH || 
            newHead.y < 0 || newHead.y >= GRID_HEIGHT || 
            isPositionOccupied(newHead, aiSnake.body.slice(1)) ||
            isPositionOccupied(newHead, playerSnake.body)) {
            continue;
        }
        
        // 计算这个方向的得分
        let score = 0;
        
        // 距离食物的距离
        const foodDistance = gameState.foodPosition ? 
            Math.abs(newHead.x - gameState.foodPosition.x) + Math.abs(newHead.y - gameState.foodPosition.y) : 
            Infinity;
        
        // 距离特殊食物的距离
        const specialFoodDistance = gameState.specialFoodPosition ? 
            Math.abs(newHead.x - gameState.specialFoodPosition.x) + Math.abs(newHead.y - gameState.specialFoodPosition.y) : 
            Infinity;
        
        // 距离玩家蛇头的距离
        const playerHeadDistance = Math.abs(newHead.x - playerSnake.body[0].x) + Math.abs(newHead.y - playerSnake.body[0].y);
        
        // 优先考虑食物
        if (foodDistance < difficultySettings.aiVisionRange) {
            score += (difficultySettings.aiVisionRange - foodDistance) * 10;
        }
        
        // 特殊食物更有价值
        if (specialFoodDistance < difficultySettings.aiVisionRange) {
            score += (difficultySettings.aiVisionRange - specialFoodDistance) * 30;
        }
        
        // 避开玩家蛇（在困难模式下更积极地避开）
        if (playerHeadDistance < 3) {
            score -= (3 - playerHeadDistance) * (gameState.difficulty === 'hard' ? 50 : 20);
        }
        
        // 避免走进死胡同
        const floodFillResult = floodFill(newHead.x, newHead.y);
        score += floodFillResult * 2;
        
        // 更新最佳方向
        if (score > bestScore) {
            bestScore = score;
            bestDirection = direction;
        }
    }
    
    // 如果找到了最佳方向，更新AI的下一个方向
    if (bestDirection) {
        aiSnake.nextDirection = bestDirection;
    }
}

// 洪水填充算法，用于评估一个位置有多少可用空间
function floodFill(startX, startY) {
    const visited = Array(GRID_WIDTH).fill().map(() => Array(GRID_HEIGHT).fill(false));
    const queue = [{ x: startX, y: startY }];
    let count = 0;
    
    // 标记蛇身占据的位置
    playerSnake.body.forEach(segment => {
        if (segment.x >= 0 && segment.x < GRID_WIDTH && segment.y >= 0 && segment.y < GRID_HEIGHT) {
            visited[segment.x][segment.y] = true;
        }
    });
    
    aiSnake.body.forEach(segment => {
        if (segment.x >= 0 && segment.x < GRID_WIDTH && segment.y >= 0 && segment.y < GRID_HEIGHT) {
            visited[segment.x][segment.y] = true;
        }
    });
    
    while (queue.length > 0 && count < 100) { // 限制计数以提高性能
        const current = queue.shift();
        
        if (current.x < 0 || current.x >= GRID_WIDTH || current.y < 0 || current.y >= GRID_HEIGHT || visited[current.x][current.y]) {
            continue;
        }
        
        visited[current.x][current.y] = true;
        count++;
        
        // 添加相邻的单元格到队列
        queue.push({ x: current.x + 1, y: current.y });
        queue.push({ x: current.x - 1, y: current.y });
        queue.push({ x: current.x, y: current.y + 1 });
        queue.push({ x: current.x, y: current.y - 1 });
    }
    
    return count;
}

// 更新游戏状态
function updateGame() {
    if (gameState.gameOver || gameState.paused) {
        return;
    }
    
    // 更新AI方向
    updateAIDirection();
    
    // 更新蛇的位置
    updateSnakePosition(playerSnake, true);
    updateSnakePosition(aiSnake, false);
    
    // 更新计分板
    updateScoreboard();
    
    // 绘制游戏
    drawGame();
}

// 绘制游戏
function drawGame() {
    // 清空画布
    ctx.fillStyle = COLORS.BACKGROUND;
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // 绘制网格
    drawGrid();
    
    // 绘制食物
    if (gameState.foodPosition) {
        drawCell(gameState.foodPosition.x, gameState.foodPosition.y, COLORS.FOOD);
    }
    
    // 绘制特殊食物
    if (gameState.specialFoodPosition) {
        drawCell(gameState.specialFoodPosition.x, gameState.specialFoodPosition.y, COLORS.SPECIAL_FOOD);
    }
    
    // 绘制玩家蛇
    drawSnake(playerSnake);
    
    // 绘制AI蛇
    drawSnake(aiSnake);
}

// 绘制网格
function drawGrid() {
    ctx.strokeStyle = COLORS.GRID;
    ctx.lineWidth = 0.5;
    
    // 绘制垂直线
    for (let x = 0; x <= GRID_WIDTH; x++) {
        ctx.beginPath();
        ctx.moveTo(x * GRID_SIZE, 0);
        ctx.lineTo(x * GRID_SIZE, GRID_HEIGHT * GRID_SIZE);
        ctx.stroke();
    }
    
    // 绘制水平线
    for (let y = 0; y <= GRID_HEIGHT; y++) {
        ctx.beginPath();
        ctx.moveTo(0, y * GRID_SIZE);
        ctx.lineTo(GRID_WIDTH * GRID_SIZE, y * GRID_SIZE);
        ctx.stroke();
    }
}

// 绘制单元格
function drawCell(x, y, color) {
    ctx.fillStyle = color;
    ctx.fillRect(x * GRID_SIZE, y * GRID_SIZE, GRID_SIZE, GRID_SIZE);
}

// 绘制蛇
function drawSnake(snake) {
    // 绘制身体
    for (let i = 1; i < snake.body.length; i++) {
        drawCell(snake.body[i].x, snake.body[i].y, snake.color);
    }
    
    // 绘制头部
    drawCell(snake.body[0].x, snake.body[0].y, snake.headColor);
}

// 键盘事件处理
function handleKeydown(event) {
    if (!gameState.running || gameState.gameOver) {
        return;
    }
    
    switch (event.key) {
        case 'ArrowUp':
            // 不允许180度转弯
            if (playerSnake.direction !== DIRECTIONS.DOWN) {
                playerSnake.nextDirection = DIRECTIONS.UP;
            }
            break;
        case 'ArrowDown':
            if (playerSnake.direction !== DIRECTIONS.UP) {
                playerSnake.nextDirection = DIRECTIONS.DOWN;
            }
            break;
        case 'ArrowLeft':
            if (playerSnake.direction !== DIRECTIONS.RIGHT) {
                playerSnake.nextDirection = DIRECTIONS.LEFT;
            }
            break;
        case 'ArrowRight':
            if (playerSnake.direction !== DIRECTIONS.LEFT) {
                playerSnake.nextDirection = DIRECTIONS.RIGHT;
            }
            break;
        case ' ':
            // 空格键暂停/继续游戏
            pauseGame();
            break;
    }
}

// 事件监听器
function setupEventListeners() {
    // 键盘控制
    document.addEventListener('keydown', handleKeydown);
    
    // 按钮控制
    startBtn.addEventListener('click', startGame);
    pauseBtn.addEventListener('click', pauseGame);
    restartBtn.addEventListener('click', restartGame);
    playAgainBtn.addEventListener('click', () => {
        gameOverModal.style.display = 'none';
        restartGame();
    });
    
    // 难度选择
    difficultySelect.addEventListener('change', () => {
        gameState.difficulty = difficultySelect.value;
        aiSnake.thinkInterval = getDifficultySettings().aiThinkInterval;
    });
}

// 初始化游戏
function init() {
    setupEventListeners();
    initGame();
}

// 当页面加载完成后初始化游戏
window.addEventListener('load', init);